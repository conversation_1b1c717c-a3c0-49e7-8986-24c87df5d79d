#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
独立测试系列编辑对话框
"""

import sys
import os

# 添加管理端路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'shuimu-admin', 'src'))

try:
    from PyQt6.QtWidgets import QApplication, QDialog
    from ui.course_window import SeriesEditDialog
    
    app = QApplication(sys.argv)
    
    # 测试数据
    test_data = {
        'id': 'test-id',
        'title': '测试系列',
        'description': '这是一个测试系列',
        'price': 99.99,
        'is_free': False,
        'is_published': True
    }
    
    # 创建对话框
    dialog = SeriesEditDialog(None, test_data)
    
    # 检查是否有is_free_check属性
    if hasattr(dialog, 'is_free_check'):
        print("✅ SeriesEditDialog 有 is_free_check 属性")
        print(f"🔧 控件类型: {type(dialog.is_free_check)}")
        print(f"🎯 当前状态: {dialog.is_free_check.isChecked()}")
    else:
        print("❌ SeriesEditDialog 没有 is_free_check 属性")
        print("📋 可用属性:")
        for attr in dir(dialog):
            if not attr.startswith('_') and 'check' in attr.lower():
                print(f"  - {attr}")
    
    # 显示对话框
    print("🖼️ 显示对话框窗口...")
    dialog.show()
    
    # 运行应用
    sys.exit(app.exec())
    
except ImportError as e:
    print(f"❌ 导入错误: {e}")
    if "PyQt6" in str(e):
        print("💡 提示: 需要安装 PyQt6")
        print("   可以运行: pip install PyQt6")
except Exception as e:
    print(f"❌ 运行错误: {e}")
    import traceback
    traceback.print_exc()