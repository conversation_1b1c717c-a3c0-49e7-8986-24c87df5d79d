#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
找到管理端真正使用的course_window.py文件
"""

import os
import sys
from pathlib import Path

def find_course_window_files():
    """查找所有course_window.py文件"""
    
    print("🔍 搜索所有course_window.py文件...")
    
    # 搜索整个D盘（或当前磁盘）
    search_paths = [
        "D:\\",  # Windows路径
        "/mnt/d/",  # WSL路径
        os.path.expanduser("~"),  # 用户目录
        "C:\\",  # C盘
        "/mnt/c/",  # WSL C盘
    ]
    
    found_files = []
    
    for search_path in search_paths:
        if os.path.exists(search_path):
            print(f"📂 搜索路径: {search_path}")
            try:
                for root, dirs, files in os.walk(search_path):
                    # 跳过系统目录和常见的无关目录
                    dirs[:] = [d for d in dirs if not d.startswith('.') and 
                              d not in ['Windows', 'System32', 'ProgramData', 'AppData', '$Recycle.Bin', 'node_modules', '__pycache__']]
                    
                    for file in files:
                        if file == "course_window.py":
                            file_path = os.path.join(root, file)
                            found_files.append(file_path)
                            print(f"  ✅ 找到: {file_path}")
            except (PermissionError, OSError) as e:
                print(f"  ❌ 无法访问: {search_path} - {e}")
            except Exception as e:
                print(f"  ⚠️ 搜索错误: {search_path} - {e}")
    
    return found_files

def analyze_course_window_file(file_path):
    """分析course_window.py文件"""
    
    print(f"\n🔍 分析文件: {file_path}")
    
    try:
        # 检查文件修改时间
        stat = os.stat(file_path)
        from datetime import datetime
        mod_time = datetime.fromtimestamp(stat.st_mtime)
        print(f"📅 修改时间: {mod_time}")
        
        # 读取文件内容
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
        
        # 检查关键特征
        features = {
            "包含免费标识控件": "self.is_free_check = QCheckBox()" in content,
            "包含免费标识表单行": "form_layout.addRow('免费标识:', self.is_free_check)" in content,
            "包含调试输出": "🔧 调试: SeriesEditDialog" in content,
            "窗口高度450": "setFixedSize(500, 450)" in content,
            "窗口高度400": "setFixedSize(500, 400)" in content,
            "包含SeriesEditDialog类": "class SeriesEditDialog" in content,
        }
        
        print("📋 特征检查:")
        for feature, exists in features.items():
            status = "✅" if exists else "❌"
            print(f"  {status} {feature}")
        
        # 计算文件特征值
        file_size = len(content)
        line_count = len(content.splitlines())
        print(f"📏 文件大小: {file_size} 字符, {line_count} 行")
        
        return {
            'path': file_path,
            'mod_time': mod_time,
            'features': features,
            'size': file_size,
            'lines': line_count
        }
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return None

def find_python_processes():
    """查找正在运行的Python进程"""
    
    print("\n🔍 查找正在运行的Python进程...")
    
    try:
        import subprocess
        
        # Windows
        try:
            result = subprocess.run(['tasklist', '/FI', 'IMAGENAME eq python.exe'], 
                                  capture_output=True, text=True, timeout=5)
            if result.returncode == 0 and 'python.exe' in result.stdout:
                print("🐍 找到Windows Python进程:")
                lines = result.stdout.split('\n')
                for line in lines:
                    if 'python.exe' in line:
                        print(f"  {line.strip()}")
        except:
            pass
        
        # Linux/WSL
        try:
            result = subprocess.run(['ps', 'aux'], capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                lines = result.stdout.split('\n')
                python_lines = [line for line in lines if 'python' in line and 'main.py' in line]
                if python_lines:
                    print("🐍 找到Linux Python进程:")
                    for line in python_lines:
                        print(f"  {line.strip()}")
        except:
            pass
            
    except Exception as e:
        print(f"❌ 进程查找失败: {e}")

if __name__ == "__main__":
    print("🚀 开始诊断管理端代码路径问题...")
    
    # 1. 查找所有course_window.py文件
    files = find_course_window_files()
    
    if not files:
        print("❌ 没有找到任何course_window.py文件")
        sys.exit(1)
    
    print(f"\n📊 总共找到 {len(files)} 个course_window.py文件")
    
    # 2. 分析每个文件
    file_analyses = []
    for file_path in files:
        analysis = analyze_course_window_file(file_path)
        if analysis:
            file_analyses.append(analysis)
    
    # 3. 找出最可能正在使用的文件
    print("\n🎯 分析结果:")
    
    modified_files = [f for f in file_analyses if f['features']['包含免费标识控件']]
    if modified_files:
        print("✅ 找到包含我们修改的文件:")
        for f in modified_files:
            print(f"  📁 {f['path']}")
            print(f"     修改时间: {f['mod_time']}")
    else:
        print("❌ 没有找到包含我们修改的文件！")
    
    unmodified_files = [f for f in file_analyses if not f['features']['包含免费标识控件']]
    if unmodified_files:
        print("⚠️ 找到未修改的文件（可能正在被使用）:")
        for f in unmodified_files:
            print(f"  📁 {f['path']}")
            print(f"     修改时间: {f['mod_time']}")
    
    # 4. 查找Python进程
    find_python_processes()
    
    print("\n💡 建议:")
    if len(file_analyses) > 1:
        print("1. 发现多个course_window.py文件，管理端可能使用了错误的文件")
        print("2. 需要确定管理端的实际启动路径")
        print("3. 将修改应用到正确的文件")
    else:
        print("1. 只有一个course_window.py文件，但修改没有生效")
        print("2. 可能是Python缓存或进程问题") 
        print("3. 尝试完全重启系统")