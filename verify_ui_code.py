#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证UI代码修改的完整性
"""

import os

def verify_ui_modifications():
    """验证UI修改的完整性"""
    
    file_path = "/mnt/d/01-shuimu_01/shuimu-admin/src/ui/course_window.py"
    
    print("🔍 验证UI代码修改...")
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 验证点1: 窗口高度
        if "setFixedSize(500, 450)" in content:
            print("✅ 窗口高度已更新为450")
        elif "setFixedSize(500, 400)" in content:
            print("❌ 窗口高度仍为400 (应该是450)")
        else:
            print("⚠️ 未找到窗口大小设置")
        
        # 验证点2: 控件定义
        if "self.is_free_check = QCheckBox()" in content:
            print("✅ 免费标识控件已定义")
        else:
            print("❌ 免费标识控件未定义")
        
        # 验证点3: 表单行
        if "form_layout.addRow('免费标识:', self.is_free_check)" in content:
            print("✅ 免费标识表单行已添加")
        else:
            print("❌ 免费标识表单行未添加")
        
        # 验证点4: 数据加载
        if "self.is_free_check.setChecked(self.series_data.get('is_free', False))" in content:
            print("✅ 免费标识数据加载已添加")
        else:
            print("❌ 免费标识数据加载未添加")
        
        # 验证点5: 数据保存
        if "'is_free': self.is_free_check.isChecked()" in content:
            print("✅ 免费标识数据保存已添加")
        else:
            print("❌ 免费标识数据保存未添加")
        
        # 验证点6: 工具提示
        if "setToolTip" in content and "免费课程" in content:
            print("✅ 工具提示已添加")
        else:
            print("⚠️ 工具提示未添加或不完整")
        
        # 验证正确的方法顺序
        lines = content.splitlines()
        init_ui_start = -1
        addrow_lines = []
        
        for i, line in enumerate(lines):
            if "def init_ui(self):" in line:
                init_ui_start = i
            if "form_layout.addRow(" in line and i > init_ui_start > 0:
                addrow_lines.append((i, line.strip()))
        
        print(f"\n📝 表单行顺序 (从第{init_ui_start + 1}行开始):")
        for line_num, line in addrow_lines:
            print(f"   {line_num + 1:3d}: {line}")
        
        # 检查是否有正确的顺序：标题、描述、价格、免费标识、发布状态
        expected_order = ["系列标题", "系列描述", "系列价格", "免费标识", "发布状态"]
        actual_order = []
        
        for _, line in addrow_lines:
            for expected in expected_order:
                if expected in line:
                    actual_order.append(expected)
                    break
        
        print(f"\n📋 期望顺序: {' → '.join(expected_order)}")
        print(f"📋 实际顺序: {' → '.join(actual_order)}")
        
        if actual_order == expected_order:
            print("✅ 表单行顺序正确")
        else:
            print("❌ 表单行顺序不正确")
        
        # 统计检查
        total_checks = 6
        passed_checks = sum([
            "setFixedSize(500, 450)" in content,
            "self.is_free_check = QCheckBox()" in content,
            "form_layout.addRow('免费标识:', self.is_free_check)" in content,
            "self.is_free_check.setChecked(self.series_data.get('is_free', False))" in content,
            "'is_free': self.is_free_check.isChecked()" in content,
            "setToolTip" in content and "免费课程" in content
        ])
        
        print(f"\n📊 验证结果: {passed_checks}/{total_checks} 项检查通过")
        
        if passed_checks == total_checks:
            print("🎉 所有UI修改验证通过！")
            print("\n💡 如果管理端仍然看不到免费标识，可能的原因：")
            print("   1. 管理端程序需要完全重启")
            print("   2. Python进程缓存问题（已清理）") 
            print("   3. 系统级别的缓存问题")
            print("   4. 管理端使用了不同的代码路径")
            
            print("\n🔧 建议的调试步骤：")
            print("   1. 确保管理端完全关闭（检查任务管理器）")
            print("   2. 重新启动管理端")
            print("   3. 如果仍然不行，在系列编辑对话框中添加调试输出")
            
        else:
            print("❌ 部分UI修改未通过验证")
            
    except Exception as e:
        print(f"❌ 验证失败: {e}")

if __name__ == "__main__":
    verify_ui_modifications()