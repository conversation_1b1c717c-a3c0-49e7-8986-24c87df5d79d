#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
强制重载UI模块
"""

import os
import sys
import importlib

# 添加管理端路径
admin_path = os.path.join(os.path.dirname(__file__), 'shuimu-admin', 'src')
if admin_path not in sys.path:
    sys.path.insert(0, admin_path)

def force_reload_ui():
    """强制重载UI模块"""
    
    print("🔄 开始强制重载UI模块...")
    
    # 要重载的模块列表
    modules_to_reload = [
        'ui.course_window',
    ]
    
    # 删除已导入的模块
    for module_name in modules_to_reload:
        if module_name in sys.modules:
            print(f"🗑️ 删除模块: {module_name}")
            del sys.modules[module_name]
    
    # 删除.pyc文件
    pyc_files = []
    for root, dirs, files in os.walk(admin_path):
        for file in files:
            if file.endswith('.pyc'):
                pyc_path = os.path.join(root, file)
                pyc_files.append(pyc_path)
                try:
                    os.remove(pyc_path)
                    print(f"🗑️ 删除.pyc文件: {pyc_path}")
                except Exception as e:
                    print(f"❌ 删除失败: {pyc_path} - {e}")
    
    # 删除__pycache__目录
    for root, dirs, files in os.walk(admin_path):
        if '__pycache__' in dirs:
            pycache_path = os.path.join(root, '__pycache__')
            try:
                import shutil
                shutil.rmtree(pycache_path)
                print(f"🗑️ 删除__pycache__目录: {pycache_path}")
            except Exception as e:
                print(f"❌ 删除失败: {pycache_path} - {e}")
    
    print(f"✅ 清理完成，删除了 {len(pyc_files)} 个.pyc文件")
    
    # 重新导入模块并检查
    try:
        print("📥 重新导入模块...")
        
        # 导入course_window模块
        import ui.course_window
        importlib.reload(ui.course_window)
        
        # 检查SeriesEditDialog类
        from ui.course_window import SeriesEditDialog
        
        print("🔍 检查SeriesEditDialog类...")
        
        # 检查类的方法
        if hasattr(SeriesEditDialog, 'init_ui'):
            print("✅ 找到init_ui方法")
            
            # 获取方法源代码
            import inspect
            source = inspect.getsource(SeriesEditDialog.init_ui)
            
            if "is_free_check" in source:
                print("✅ init_ui方法包含is_free_check")
            else:
                print("❌ init_ui方法不包含is_free_check")
                
            if "免费标识" in source:
                print("✅ init_ui方法包含免费标识标签")
            else:
                print("❌ init_ui方法不包含免费标识标签")
                
            # 显示关键行
            lines = source.split('\n')
            for i, line in enumerate(lines):
                if 'is_free_check' in line or '免费标识' in line:
                    print(f"📝 第{i+1}行: {line.strip()}")
        else:
            print("❌ 没有找到init_ui方法")
            
    except Exception as e:
        print(f"❌ 重新导入失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    force_reload_ui()
    
    print("\n🎯 解决方案:")
    print("1. 运行此脚本后，完全关闭管理端程序")
    print("2. 重新启动管理端程序")
    print("3. 进入课程管理 → 系列管理 → 编辑系列")
    print("4. 应该能看到免费标识复选框")